import { Badge } from "../components/ui/badge";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { But<PERSON> } from "../components/ui/button";
import { <PERSON>, TrendingUp, Al<PERSON><PERSON>riangle, <PERSON>bulb, <PERSON><PERSON><PERSON> } from "lucide-react";

interface AIInsight {
  id: string;
  type: 'optimization' | 'alert' | 'prediction' | 'recommendation';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  confidence: number;
}

const mockInsights: AIInsight[] = [
  {
    id: '1',
    type: 'optimization',
    title: 'Power Factor Optimization',
    description: 'Installing a capacitor bank could improve power factor to 0.99, reducing energy costs by 8%',
    impact: 'high',
    confidence: 92
  },
  {
    id: '2',
    type: 'prediction',
    title: 'Maintenance Forecast',
    description: 'Based on current patterns, device may require maintenance in 14 days',
    impact: 'medium',
    confidence: 87
  },
  {
    id: '3',
    type: 'alert',
    title: 'Peak Load Warning',
    description: 'Approaching maximum capacity during 2-4 PM timeframe',
    impact: 'high',
    confidence: 95
  },
  {
    id: '4',
    type: 'recommendation',
    title: 'Scheduling Optimization',
    description: 'Shifting 15% of load to off-peak hours could save $150/month',
    impact: 'medium',
    confidence: 84
  }
];

const getInsightIcon = (type: AIInsight['type']) => {
  switch (type) {
    case 'optimization':
      return <TrendingUp className="h-4 w-4" />;
    case 'alert':
      return <AlertTriangle className="h-4 w-4" />;
    case 'prediction':
      return <BarChart className="h-4 w-4" />;
    case 'recommendation':
      return <Lightbulb className="h-4 w-4" />;
    default:
      return <Brain className="h-4 w-4" />;
  }
};

const getInsightColor = (type: AIInsight['type']) => {
  switch (type) {
    case 'optimization':
      return 'text-blue-600 bg-blue-50';
    case 'alert':
      return 'text-orange-600 bg-orange-50';
    case 'prediction':
      return 'text-purple-600 bg-purple-50';
    case 'recommendation':
      return 'text-green-600 bg-green-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

const getBadgeVariant = (impact: AIInsight['impact']) => {
  switch (impact) {
    case 'high':
      return 'destructive' as const;
    case 'medium':
      return 'default' as const;
    case 'low':
      return 'secondary' as const;
    default:
      return 'outline' as const;
  }
};

export function AIInsights() {
  return (
    <Card className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50/30 to-blue-50/30" />
      <CardHeader className="relative">
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          AI Insights & Recommendations
        </CardTitle>
        <CardDescription>
          Machine learning powered analytics and suggestions
        </CardDescription>
      </CardHeader>
      <CardContent className="relative space-y-4">
        {mockInsights.map((insight) => (
          <div 
            key={insight.id}
            className={`p-4 rounded-lg border ${getInsightColor(insight.type)}`}
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                {getInsightIcon(insight.type)}
                <h4 className="font-semibold text-sm">{insight.title}</h4>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={getBadgeVariant(insight.impact)}>
                  {insight.impact} impact
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {insight.confidence}% confidence
                </Badge>
              </div>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {insight.description}
            </p>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-current rounded-full opacity-60" />
                <span className="text-xs text-muted-foreground capitalize">
                  {insight.type}
                </span>
              </div>
              <Button variant="outline" size="sm" className="h-7 text-xs">
                View Details
              </Button>
            </div>
          </div>
        ))}
        
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              AI Analysis updated 2 minutes ago
            </div>
            <Button variant="outline" size="sm">
              <Brain className="w-4 h-4 mr-2" />
              Refresh Analysis
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
