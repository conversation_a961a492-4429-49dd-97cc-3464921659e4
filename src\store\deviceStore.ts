import { create } from 'zustand';

interface Device {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'maintenance';
}

interface DeviceState {
  devices: Device[];
  setDevices: (data: Device[]) => void;
  updateDevice: (device: Device) => void;
}

export const useDeviceStore = create<DeviceState>((set) => ({
  devices: [],
  setDevices: (data) => set({ devices: data }),
  updateDevice: (device) =>
    set((state) => ({
      devices: state.devices.map((d) =>
        d.id === device.id ? device : d
      ),
    })),
}));
