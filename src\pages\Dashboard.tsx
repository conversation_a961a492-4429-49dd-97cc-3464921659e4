import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { getIotData, addIotData } from "../services/iotService";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { Activity } from "lucide-react";
import { DemoIoTChart } from "../components/charts/DemoIoTChart";
import { IoTMetricsCards } from "../components/IoTMetricsCards";
import { DeviceStatus } from "../components/DeviceStatus";
import { AIInsights } from "../components/AIInsights";
import { ErrorBoundary } from "../components/ErrorBoundary";
import { useDeviceStore } from "../stores/deviceStore";

type IotDataInput = {
  voltage: number;
  current: number;
  power_operating: number;
  energy: number;
  frequency: number;
  power_factor: number;
};

type IotDataItem = IotDataInput & {
  id: string | number;
  created_at: string;
};

const Dashboard = () => {
  const queryClient = useQueryClient();
  const { currentDevice, initializeDevices } = useDeviceStore();

  // Initialize devices when component mounts
  useEffect(() => {
    initializeDevices();
  }, [initializeDevices]);

  // Use current device or fallback
  const deviceName = currentDevice?.id || "auo-display";

  const { data, isLoading, error } = useQuery({
    queryKey: ["iot", deviceName],
    queryFn: () => getIotData(deviceName),
  });

  const mutation = useMutation({
    mutationFn: (newData: IotDataInput) => addIotData(deviceName, newData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["iot", deviceName] });
    },
    onError: (err: unknown) => {
      console.error("Failed to add IoT data:", err);
    },
  });

  const handleAdd = () => {
    mutation.mutate({
      voltage: 120,
      current: 1.1,
      power_operating: 200,
      energy: 4.2,
      frequency: 50,
      power_factor: 0.97,
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-red-600">Error: {(error as Error).message}</div>
        </CardContent>
      </Card>
    );
  }

  // Get device metrics from current device or use default data
  const latestData = currentDevice?.metrics || data?.[data.length - 1] || {
    voltage: 220.5,
    current: 1.1,
    power_operating: 242.5,
    frequency: 50.0,
    temperature: 45
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">IoMT Dashboard</h1>
          <p className="text-muted-foreground">
            Monitoring device: {currentDevice?.name || deviceName}
          </p>
        </div>
        <Button onClick={handleAdd} className="ml-auto">
          <Activity className="mr-2 h-4 w-4" />
          Simulate Data
        </Button>
      </div>

      {/* IoT Metrics Cards */}
      <IoTMetricsCards data={latestData} />

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-4">
        {/* Charts Section - Takes 3 columns */}
        <div className="lg:col-span-3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Power Consumption</CardTitle>
              <CardDescription>
                Real-time power consumption monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ErrorBoundary>
                <DemoIoTChart deviceName={deviceName} />
              </ErrorBoundary>
            </CardContent>
          </Card>

          {/* AI Insights Section */}
          <AIInsights />
        </div>

        {/* Sidebar Content - Takes 1 column */}
        <div className="lg:col-span-1 space-y-6">
          <DeviceStatus 
            deviceName={currentDevice?.name || deviceName}
            isOnline={currentDevice?.status === 'online'}
            lastUpdate={currentDevice?.lastSeen || "Just now"}
          />
          
          <Card>
            <CardHeader>
              <CardTitle>Recent Data</CardTitle>
              <CardDescription>
                Latest readings from {currentDevice?.name || deviceName}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.slice(-5).map((item: IotDataItem, index: number) => (
                  <div key={item.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                    <div className="text-sm">
                      <div className="font-medium">Reading #{data.length - 4 + index}</div>
                      <div className="text-muted-foreground">
                        {new Date(item.created_at).toLocaleTimeString()}
                      </div>
                    </div>
                    <div className="text-right text-sm">
                      <div className="font-mono">{item.voltage}V</div>
                      <div className="text-muted-foreground">{item.power_operating}W</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Device Info Card */}
          <Card>
            <CardHeader>
              <CardTitle>Device Info</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium">{currentDevice?.type || 'Power Monitor'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Location:</span>
                  <span className="font-medium">{currentDevice?.location || 'Unknown'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className={`font-medium capitalize ${
                    currentDevice?.status === 'online' ? 'text-green-600' : 
                    currentDevice?.status === 'offline' ? 'text-red-600' : 
                    'text-orange-600'
                  }`}>
                    {currentDevice?.status || 'Unknown'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
