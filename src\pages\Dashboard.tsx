import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { getIotData, addIotData, generateMockData, DeviceType } from "../services/iotService";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { Activity } from "lucide-react";
import { DemoIoTChart } from "../components/charts/DemoIoTChart";
import { IoTMetricsCards } from "../components/IoTMetricsCards";
import { DeviceStatus } from "../components/DeviceStatus";
import { AIInsights } from "../components/AIInsights";
import { ErrorBoundary } from "../components/ErrorBoundary";
import { DeviceMetricsDisplay } from "../components/DeviceMetricsDisplay";
import { useDeviceStore } from "../stores/deviceStore";
import { useTranslation } from "../hooks/useTranslation";
import { logI18nStatus } from "../utils/i18nTest";

type IotDataInput = {
  voltage: number;
  current: number;
  power_operating: number;
  energy: number;
  frequency: number;
  power_factor: number;
};

type IotDataItem = IotDataInput & {
  id: string | number;
  created_at: string;
};

const Dashboard = () => {
  const queryClient = useQueryClient();
  const { currentDevice, initializeDevices } = useDeviceStore();
  const { t } = useTranslation();

  // Initialize devices when component mounts
  useEffect(() => {
    initializeDevices();
    // Test i18n status in development
    if (process.env.NODE_ENV === 'development') {
      logI18nStatus();
    }
  }, [initializeDevices]);

  // Use current device or fallback
  const deviceName = currentDevice?.id || "auo-display";

  const { data, isLoading, error } = useQuery({
    queryKey: ["iot", deviceName],
    queryFn: () => getIotData(deviceName as DeviceType),
    refetchInterval: 10000, // Refetch every 10 seconds for real-time updates
    refetchIntervalInBackground: true, // Continue refetching when tab is not active
    staleTime: 5000, // Data is fresh for 5 seconds
  });

  const mutation = useMutation({
    mutationFn: (newData: IotDataInput) => addIotData(deviceName, newData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["iot", deviceName] });
    },
    onError: (err: unknown) => {
      console.error("Failed to add IoT data:", err);
    },
  });

  const handleAdd = () => {
    const mockData = generateMockData(deviceName as DeviceType);
    mutation.mutate(mockData);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-red-600">Error: {(error as Error).message}</div>
        </CardContent>
      </Card>
    );
  }

  // Get device metrics from current device or use default data
  const latestData = currentDevice?.metrics || data?.[data.length - 1] || {
    voltage: 220.5,
    current: 1.1,
    power_operating: 242.5,
    frequency: 50.0,
    temperature: 45
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{t("dashboard.title")}</h1>
          <p className="text-muted-foreground">
            {t("dashboard.subtitle", { deviceName: currentDevice?.name || "Unknown Device" })}
          </p>
        </div>
        <Button onClick={handleAdd} className="w-fit">
          <Activity className="mr-2 h-4 w-4" />
          {t("dashboard.simulateData")}
        </Button>
      </div>

      {/* IoT Metrics Cards */}
      <IoTMetricsCards data={latestData} />

      {/* Main Content Grid */}
      <div className="grid gap-6 xl:grid-cols-4 lg:grid-cols-3">
        {/* Charts Section - Takes 3 columns on xl, 2 on lg, full on smaller */}
        <div className="xl:col-span-3 lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("dashboard.charts.powerConsumption")}</CardTitle>
              <CardDescription>
                {t("dashboard.charts.realTimePowerConsumptionMonitoring")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ErrorBoundary>
                <DemoIoTChart deviceName={deviceName} />
              </ErrorBoundary>
            </CardContent>
          </Card>

          {/* AI Insights Section */}
          <AIInsights />
        </div>

        {/* Sidebar Content - Takes 1 column on xl, 1 on lg, full on smaller */}
        <div className="xl:col-span-1 lg:col-span-1 space-y-6">
          <DeviceStatus
            deviceName={currentDevice?.name || deviceName}
            isOnline={currentDevice?.status === 'online'}
            lastUpdate={currentDevice?.lastSeen || "Just now"}
          />
          
          {/* Device Metrics Display */}
          <DeviceMetricsDisplay
            data={data || []}
            deviceName={currentDevice?.name || "Unknown Device"}
            isLoading={isLoading}
          />

          {/* Device Info Card */}
          <Card>
            <CardHeader>
              <CardTitle>Device Info</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium">{currentDevice?.type || 'Power Monitor'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Location:</span>
                  <span className="font-medium">{currentDevice?.location || 'Unknown'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className={`font-medium capitalize ${
                    currentDevice?.status === 'online' ? 'text-green-600' : 
                    currentDevice?.status === 'offline' ? 'text-red-600' : 
                    'text-orange-600'
                  }`}>
                    {currentDevice?.status || 'Unknown'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
