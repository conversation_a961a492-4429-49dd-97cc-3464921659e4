import { ChevronDown, Cpu, AlertCircle, CheckCircle } from "lucide-react"
import { useDeviceStore, type Device } from "../stores/deviceStore"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "../components/ui/sidebar"
import { Badge } from "../components/ui/badge"

export function DeviceSwitcher() {
  const { isMobile } = useSidebar()
  const { devices, currentDevice, setCurrentDevice } = useDeviceStore()

  const getStatusIcon = (status: Device['status']) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-3 w-3 text-green-500" />
      case 'offline':
        return <AlertCircle className="h-3 w-3 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-3 w-3 text-orange-500" />
      default:
        return <Cpu className="h-3 w-3" />
    }
  }

  const getStatusBadge = (status: Device['status']) => {
    switch (status) {
      case 'online':
        return <Badge variant="default" className="bg-green-600 text-xs">Online</Badge>
      case 'offline':
        return <Badge variant="destructive" className="text-xs">Offline</Badge>
      case 'warning':
        return <Badge variant="outline" className="text-orange-600 border-orange-300 text-xs">Warning</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">Unknown</Badge>
    }
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Cpu className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {currentDevice?.name || 'Select Device'}
                </span>
                <span className="truncate text-xs text-muted-foreground">
                  {currentDevice?.location || 'No device selected'}
                </span>
              </div>
              <ChevronDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            {devices.map((device) => (
              <DropdownMenuItem
                key={device.id}
                onClick={() => setCurrentDevice(device)}
                className="gap-2 p-3"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-muted">
                  {getStatusIcon(device.status)}
                </div>
                <div className="grid flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{device.name}</span>
                    {getStatusBadge(device.status)}
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {device.type} • {device.location}
                  </span>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
