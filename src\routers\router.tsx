// router.tsx
import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import AppLayout from "../layouts/AppLayout";
import Dashboard from "../pages/Dashboard";
import DevicesPage from "../pages/DevicesPage";
import AnalyticsPage from "../pages/AnalyticsPage";
import DeviceSelectionPage from "../pages/DeviceSelectionPage";

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Device Selection Page - No Layout */}
      <Route path="/" element={<DeviceSelectionPage />} />

      {/* Dashboard Pages - With Layout */}
      <Route path="/dashboard" element={<AppLayout><Dashboard /></AppLayout>} />
      <Route path="/devices" element={<AppLayout><DevicesPage /></AppLayout>} />
      <Route path="/analytics" element={<AppLayout><AnalyticsPage /></AppLayout>} />
      <Route path="/assets" element={<AppLayout><div className="p-6"><h1 className="text-2xl font-bold">Assets Page</h1><p>Coming soon...</p></div></AppLayout>} />
      <Route path="/customers" element={<AppLayout><div className="p-6"><h1 className="text-2xl font-bold">Customers Page</h1><p>Coming soon...</p></div></AppLayout>} />
      <Route path="/settings" element={<AppLayout><div className="p-6"><h1 className="text-2xl font-bold">Settings Page</h1><p>Coming soon...</p></div></AppLayout>} />
    </Routes>
  );
};

export default AppRoutes;
