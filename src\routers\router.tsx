// router.tsx
import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import AppLayout from "../layouts/AppLayout";
import Dashboard from "../pages/Dashboard";
import DevicesPage from "../pages/DevicesPage";
import AnalyticsPage from "../pages/AnalyticsPage";

const AppRoutes: React.FC = () => {
  return (
    <AppLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/devices" element={<DevicesPage />} />
        <Route path="/analytics" element={<AnalyticsPage />} />
        <Route path="/assets" element={<div className="p-6"><h1 className="text-2xl font-bold">Assets Page</h1><p>Coming soon...</p></div>} />
        <Route path="/customers" element={<div className="p-6"><h1 className="text-2xl font-bold">Customers Page</h1><p>Coming soon...</p></div>} />
        <Route path="/settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings Page</h1><p>Coming soon...</p></div>} />
      </Routes>
    </AppLayout>
  );
};

export default AppRoutes;
