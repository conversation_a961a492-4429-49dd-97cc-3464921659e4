import * as React from "react"
import {
  Activity,
  BarChart3,
  Bell,
  Clipboard,
  Cpu,
  Database,
  File,
  HelpCircle,
  LayoutDashboard,
  Search,
  Settings,
  Shield,
  Users,
  Wifi,
} from "lucide-react"

import { NavDocuments } from "../components/nav-documents"
import { NavMain } from "../components/nav-main"
import { NavSecondary } from "../components/nav-secondary"
import { NavUser } from "../components/nav-user"
import { DeviceSwitcher } from "../components/DeviceSwitcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../components/ui/sidebar"

const data = {
  user: {
    name: "IoT Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Devices",
      url: "/devices",
      icon: Cpu,
      items: [
        {
          title: "All Devices",
          url: "/devices",
        },
        {
          title: "Device Groups", 
          url: "/devices/groups",
        },
        {
          title: "Device Profiles",
          url: "/devices/profiles",
        },
      ],
    },
    {
      title: "Analytics",
      url: "/analytics",
      icon: BarChart3,
    },
    {
      title: "Assets",
      url: "/assets",
      icon: Database,
    },
    {
      title: "Customers",
      url: "/customers",
      icon: Users,
    },
  ],
  navClouds: [
    {
      title: "Monitoring",
      icon: Activity,
      isActive: true,
      url: "/monitoring",
      items: [
        {
          title: "Real-time",
          url: "/monitoring/realtime",
        },
        {
          title: "Historical",
          url: "/monitoring/historical",
        },
      ],
    },
    {
      title: "Alarms",
      icon: Bell,
      url: "/alarms",
      items: [
        {
          title: "Active Alarms",
          url: "/alarms/active",
        },
        {
          title: "Alarm History",
          url: "/alarms/history",
        },
      ],
    },
    {
      title: "Rules",
      icon: Shield,
      url: "/rules",
      items: [
        {
          title: "Rule Chains",
          url: "/rules/chains",
        },
        {
          title: "Rule Engine",
          url: "/rules/engine",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
    },
    {
      title: "Get Help",
      url: "/help",
      icon: HelpCircle,
    },
    {
      title: "Search",
      url: "/search",
      icon: Search,
    },
  ],
  documents: [
    {
      name: "Data Sources",
      url: "/datasources",
      icon: Database,
    },
    {
      name: "Reports",
      url: "/reports",
      icon: Clipboard,
    },
    {
      name: "API Explorer",
      url: "/api",
      icon: File,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="none" {...props}>
      <SidebarHeader>
        <DeviceSwitcher />
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <Wifi className="h-5 w-5" />
                <span className="text-base font-semibold">IoMT Platform</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
