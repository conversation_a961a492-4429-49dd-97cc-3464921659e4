import { useEffect } from 'react';
import { io } from 'socket.io-client';
import { useDeviceStore } from '../store/deviceStore';

export const useSocket = () => {
  const updateDevice = useDeviceStore((s) => s.updateDevice);

  useEffect(() => {
    const socket = io('http://localhost:4000'); // chỉnh theo backend

    socket.on('device-status', (deviceUpdate) => {
      updateDevice(deviceUpdate);
    });

    return () => {
      socket.disconnect();
    };
  }, [updateDevice]);
};
