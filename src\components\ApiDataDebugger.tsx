import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Bug, Database } from 'lucide-react';
import type { IoTData } from '../types/iot';

interface ApiDataDebuggerProps {
  data: IoTData[];
  deviceName: string;
}

export const ApiDataDebugger: React.FC<ApiDataDebuggerProps> = ({
  data,
  deviceName
}) => {
  if (process.env.NODE_ENV !== 'development') {
    return null; // Only show in development
  }

  const latestData = data.length > 0 ? data[data.length - 1] : null;

  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <Bug className="h-5 w-5" />
          API Data Debugger - {deviceName}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Badge variant="outline" className="mb-2">
              <Database className="h-3 w-3 mr-1" />
              Data Stats
            </Badge>
            <div className="text-sm space-y-1">
              <div>Total Records: <span className="font-mono">{data.length}</span></div>
              <div>Has Data: <span className="font-mono">{data.length > 0 ? 'Yes' : 'No'}</span></div>
            </div>
          </div>
          
          {latestData && (
            <div>
              <Badge variant="outline" className="mb-2">Latest Data</Badge>
              <div className="text-xs space-y-1 font-mono">
                <div>ID: {latestData.id}</div>
                <div>Voltage: {typeof latestData.voltage} = {latestData.voltage}</div>
                <div>Current: {typeof latestData.current} = {latestData.current}</div>
                <div>Power: {typeof latestData.power_operating} = {latestData.power_operating}</div>
                <div>Energy: {typeof latestData.energy} = {latestData.energy}</div>
                <div>Frequency: {typeof latestData.frequency} = {latestData.frequency}</div>
                <div>Power Factor: {typeof latestData.power_factor} = {latestData.power_factor}</div>
                <div>Operating Time: {latestData.operating_time}</div>
              </div>
            </div>
          )}
        </div>

        {latestData && (
          <div>
            <Badge variant="outline" className="mb-2">Raw JSON</Badge>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(latestData, null, 2)}
            </pre>
          </div>
        )}

        {data.length === 0 && (
          <div className="text-center py-4 text-orange-600">
            <Database className="h-8 w-8 mx-auto mb-2" />
            <div className="font-medium">No API Data Available</div>
            <div className="text-sm">Check API connection and device status</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
