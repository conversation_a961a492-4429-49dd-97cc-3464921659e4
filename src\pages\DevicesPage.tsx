import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { But<PERSON> } from "../components/ui/button";
import { Cpu, Wifi, Battery, Thermometer, Wind } from "lucide-react";

const mockDevices = [
  {
    id: 1,
    name: "AUO Display Sensor",
    type: "Power Monitor",
    status: "online",
    lastSeen: "2 minutes ago",
    location: "Building A - Floor 2",
    metrics: {
      voltage: "220V",
      current: "1.1A",
      power: "242W",
      temperature: "45°C"
    }
  },
  {
    id: 2,
    name: "Environmental Sensor 001",
    type: "Climate Monitor",
    status: "online", 
    lastSeen: "1 minute ago",
    location: "Building A - Floor 1",
    metrics: {
      temperature: "24°C",
      humidity: "65%",
      pressure: "1013 hPa",
      air_quality: "Good"
    }
  },
  {
    id: 3,
    name: "Vibration Sensor 003",
    type: "Machine Monitor",
    status: "offline",
    lastSeen: "2 hours ago",
    location: "Factory Floor",
    metrics: {
      vibration: "0.2mm/s",
      temperature: "52°C",
      rpm: "1450",
      status: "Maintenance"
    }
  },
  {
    id: 4,
    name: "Smart Gateway 001",
    type: "Network Gateway",
    status: "online",
    lastSeen: "30 seconds ago", 
    location: "Server Room",
    metrics: {
      uptime: "99.9%",
      connected_devices: "12",
      network_status: "Strong",
      memory_usage: "45%"
    }
  }
];

export default function DevicesPage() {
  return (
    <div className="flex flex-1 flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Devices</h1>
          <p className="text-muted-foreground">
            Manage and monitor your IoT devices
          </p>
        </div>
        <Button>
          <Cpu className="mr-2 h-4 w-4" />
          Add Device
        </Button>
      </div>

      {/* Device Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="secondary">All connected</Badge>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
            <Wifi className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">3</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="default">75% uptime</Badge>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Offline</CardTitle>
            <Battery className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">1</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="destructive">Needs attention</Badge>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Points</CardTitle>
            <Thermometer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="outline">Last 24h</Badge>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Device Cards */}
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        {mockDevices.map((device) => (
          <Card key={device.id} className="col-span-1">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {device.type === "Power Monitor" && <Cpu className="h-5 w-5" />}
                    {device.type === "Climate Monitor" && <Thermometer className="h-5 w-5" />}
                    {device.type === "Machine Monitor" && <Wind className="h-5 w-5" />}
                    {device.type === "Network Gateway" && <Wifi className="h-5 w-5" />}
                    {device.name}
                  </CardTitle>
                  <CardDescription>{device.type}</CardDescription>
                </div>
                <Badge 
                  variant={device.status === "online" ? "default" : "destructive"}
                  className={device.status === "online" ? "bg-green-600" : ""}
                >
                  {device.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Location:</span>
                  <span>{device.location}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Last seen:</span>
                  <span>{device.lastSeen}</span>
                </div>
                
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  {Object.entries(device.metrics).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="text-lg font-semibold">{value}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {key.replace('_', ' ')}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2 pt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    Configure
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    View Data
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
