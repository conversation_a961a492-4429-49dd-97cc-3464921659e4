import api from './api';
import type { IoTData, IoTDataInput, DeviceType } from '../types/iot';
import { DEVICE_INFO } from '../types/iot';

// Re-export types for convenience
export type { IoTData, IoTDataInput, DeviceType };
export { DEVICE_INFO };

// API Functions
export const getIotData = async (nameField: DeviceType): Promise<IoTData[]> => {
  try {
    const res = await api.get(`/iot/${nameField}`);
    console.log(`🔍 API Response for ${nameField}:`, res.data);

    // Handle different response formats
    if (res.data.success && res.data.data) {
      return res.data.data;
    } else if (Array.isArray(res.data)) {
      return res.data;
    } else if (res.data.data) {
      return res.data.data;
    } else {
      console.warn(`Unexpected API response format for ${nameField}:`, res.data);
      return [];
    }
  } catch (error) {
    console.error(`Error fetching data for ${nameField}:`, error);
    throw error;
  }
};

export const addIotData = async (nameField: DeviceType, data: IoTDataInput): Promise<{ id: number }> => {
  try {
    const res = await api.post(`/iot/${nameField}`, data);
    console.log(`✅ POST Response for ${nameField}:`, res.data);

    // Handle different response formats
    if (res.data.success && res.data.data) {
      return res.data.data;
    } else if (res.data.id) {
      return res.data;
    } else {
      console.warn(`Unexpected POST response format for ${nameField}:`, res.data);
      return { id: 0 };
    }
  } catch (error) {
    console.error(`Error adding data for ${nameField}:`, error);
    throw error;
  }
};

// Helper function to get latest data point
export const getLatestIotData = async (nameField: DeviceType): Promise<IoTData | null> => {
  try {
    const data = await getIotData(nameField);
    return data.length > 0 ? data[data.length - 1] : null;
  } catch (error) {
    console.error(`Error fetching latest data for ${nameField}:`, error);
    return null;
  }
};

// Generate mock data for testing
export const generateMockData = (deviceType: DeviceType): IoTDataInput => {
  const baseValues = {
    'auo-display': { voltage: 220, current: 1.1, power: 242, energy: 1250, frequency: 50, pf: 0.97 },
    'camera-control': { voltage: 24, current: 2.5, power: 60, energy: 850, frequency: 50, pf: 0.95 },
    'electronic': { voltage: 12, current: 0.8, power: 9.6, energy: 420, frequency: 50, pf: 0.92 },
    'led-nova': { voltage: 5, current: 12, power: 60, energy: 2100, frequency: 50, pf: 0.98 }
  };

  const base = baseValues[deviceType];
  const variation = 0.1; // 10% variation

  return {
    voltage: base.voltage * (1 + (Math.random() - 0.5) * variation),
    current: base.current * (1 + (Math.random() - 0.5) * variation),
    power_operating: base.power * (1 + (Math.random() - 0.5) * variation),
    energy: base.energy + Math.random() * 100,
    frequency: base.frequency * (1 + (Math.random() - 0.5) * 0.02), // 2% variation for frequency
    power_factor: Math.min(1, base.pf * (1 + (Math.random() - 0.5) * 0.05)) // 5% variation, max 1
  };
};
