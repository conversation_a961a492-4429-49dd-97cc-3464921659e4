import api from './api';

// IoT API Types
export interface IoTData {
  id: number;
  voltage: number;
  current: number;
  power_operating: number;
  energy: number;
  frequency: number;
  power_factor: number;
  operating_time: string; // "hh:mm:ss" format from server
}

export interface IoTDataInput {
  voltage: number;
  current: number;
  power_operating: number;
  energy: number;
  frequency: number;
  power_factor: number;
}

export type DeviceType = 'auo-display' | 'camera-control' | 'electronic' | 'led-nova';

// Device information
export const DEVICE_INFO = {
  'auo-display': {
    name: 'AUG Display Sensor',
    type: 'Power Monitor',
    description: 'Display power monitoring sensor',
    icon: '📺'
  },
  'camera-control': {
    name: 'Camera Control Unit',
    type: 'Control System',
    description: 'Camera control and monitoring system',
    icon: '📹'
  },
  'electronic': {
    name: 'Electronic Monitor',
    type: 'Electronic System',
    description: 'General electronic device monitoring',
    icon: '⚡'
  },
  'led-nova': {
    name: 'LED Nova Controller',
    type: 'LED Controller',
    description: 'LED display controller and monitor',
    icon: '💡'
  }
} as const;

// API Functions
export const getIotData = async (nameField: DeviceType): Promise<IoTData[]> => {
  try {
    const res = await api.get(`/iot/${nameField}`);
    return res.data.data || res.data;
  } catch (error) {
    console.error(`Error fetching data for ${nameField}:`, error);
    throw error;
  }
};

export const addIotData = async (nameField: DeviceType, data: IoTDataInput): Promise<{ id: number }> => {
  try {
    const res = await api.post(`/iot/${nameField}`, data);
    return res.data;
  } catch (error) {
    console.error(`Error adding data for ${nameField}:`, error);
    throw error;
  }
};

// Helper function to get latest data point
export const getLatestIotData = async (nameField: DeviceType): Promise<IoTData | null> => {
  try {
    const data = await getIotData(nameField);
    return data.length > 0 ? data[data.length - 1] : null;
  } catch (error) {
    console.error(`Error fetching latest data for ${nameField}:`, error);
    return null;
  }
};

// Generate mock data for testing
export const generateMockData = (deviceType: DeviceType): IoTDataInput => {
  const baseValues = {
    'auo-display': { voltage: 220, current: 1.1, power: 242, energy: 1250, frequency: 50, pf: 0.97 },
    'camera-control': { voltage: 24, current: 2.5, power: 60, energy: 850, frequency: 50, pf: 0.95 },
    'electronic': { voltage: 12, current: 0.8, power: 9.6, energy: 420, frequency: 50, pf: 0.92 },
    'led-nova': { voltage: 5, current: 12, power: 60, energy: 2100, frequency: 50, pf: 0.98 }
  };

  const base = baseValues[deviceType];
  const variation = 0.1; // 10% variation

  return {
    voltage: base.voltage * (1 + (Math.random() - 0.5) * variation),
    current: base.current * (1 + (Math.random() - 0.5) * variation),
    power_operating: base.power * (1 + (Math.random() - 0.5) * variation),
    energy: base.energy + Math.random() * 100,
    frequency: base.frequency * (1 + (Math.random() - 0.5) * 0.02), // 2% variation for frequency
    power_factor: Math.min(1, base.pf * (1 + (Math.random() - 0.5) * 0.05)) // 5% variation, max 1
  };
};
