"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"
import { useQuery } from "@tanstack/react-query"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../../components/ui/chart"
import type { ChartConfig } from "../../components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "../../components/ui/toggle-group"
import { getIotData } from "../../services/iotService"

type ChartDataItem = {
  date: string
  voltage: number
  power: number
  current: number
  timestamp: number
}

const chartConfig = {
  voltage: {
    label: "Voltage (V)",
    color: "hsl(var(--chart-1))",
  },
  power: {
    label: "Power (W)",
    color: "hsl(var(--chart-2))",
  },
  current: {
    label: "Current (A)",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig

interface IoTChartProps {
  deviceName: string
}

export function IoTChart({ deviceName }: IoTChartProps) {
  const [timeRange, setTimeRange] = React.useState("30d")
  const [selectedMetric, setSelectedMetric] = React.useState<"voltage" | "power" | "current">("voltage")

  const { data: iotData, isLoading, error } = useQuery({
    queryKey: ["iot", deviceName],
    queryFn: () => getIotData(deviceName),
    retry: 1,
    // Don't refetch on window focus to avoid too many requests during development
    refetchOnWindowFocus: false,
  })

  const chartData = React.useMemo(() => {
    if (!iotData) return []
    
    return iotData.map((item: any, index: number) => {
      // Validate and create date
      let date: Date;
      let timestamp: number;
      
      if (item.created_at) {
        date = new Date(item.created_at);
        if (isNaN(date.getTime())) {
          // If invalid date, use current time minus index hours for demo
          date = new Date(Date.now() - (index * 60 * 60 * 1000));
        }
      } else {
        // If no created_at, use current time minus index hours for demo
        date = new Date(Date.now() - (index * 60 * 60 * 1000));
      }
      
      timestamp = date.getTime();
      
      return {
        date: date.toISOString().split('T')[0],
        voltage: item.voltage || 0,
        power: item.power_operating || 0,
        current: item.current || 0,
        timestamp: timestamp,
      }
    })
  }, [iotData])

  const filteredData = React.useMemo(() => {
    const now = new Date()
    let daysToSubtract = 30
    
    if (timeRange === "7d") {
      daysToSubtract = 7
    } else if (timeRange === "24h") {
      daysToSubtract = 1
    }
    
    const startDate = new Date(now)
    startDate.setDate(startDate.getDate() - daysToSubtract)
    
    return chartData.filter((item: ChartDataItem) => item.timestamp >= startDate.getTime())
  }, [chartData, timeRange])

  if (isLoading) {
    return (
      <Card className="@container/card">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="@container/card border-amber-200 bg-amber-50">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center h-32 text-amber-700">
            <p className="text-sm font-medium">Unable to load chart data</p>
            <p className="text-xs text-amber-600 mt-1">Using simulated data for demo</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If no data, show demo data
  if (!iotData || iotData.length === 0) {
    return (
      <Card className="@container/card border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center h-32 text-blue-700">
            <p className="text-sm font-medium">No data available</p>
            <p className="text-xs text-blue-600 mt-1">Start collecting data from your IoT devices</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="@container/card">
      <CardHeader className="relative">
        <CardTitle>IoT Metrics - {deviceName}</CardTitle>
        <CardDescription>
          <span className="@[540px]/card:block hidden">
            Real-time monitoring data
          </span>
          <span className="@[540px]/card:hidden">Real-time data</span>
        </CardDescription>
        <div className="absolute right-4 top-4 space-y-2">
          <Select value={selectedMetric} onValueChange={(value: "voltage" | "power" | "current") => setSelectedMetric(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="voltage">Voltage</SelectItem>
              <SelectItem value="power">Power</SelectItem>
              <SelectItem value="current">Current</SelectItem>
            </SelectContent>
          </Select>
          
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="@[767px]/card:flex hidden"
          >
            <ToggleGroupItem value="24h" className="h-8 px-2.5">
              24h
            </ToggleGroupItem>
            <ToggleGroupItem value="7d" className="h-8 px-2.5">
              7 days
            </ToggleGroupItem>
            <ToggleGroupItem value="30d" className="h-8 px-2.5">
              30 days
            </ToggleGroupItem>
          </ToggleGroup>
          
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="@[767px]/card:hidden flex w-32"
              aria-label="Select time range"
            >
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="24h" className="rounded-lg">
                Last 24 hours
              </SelectItem>
              <SelectItem value="7d" className="rounded-lg">
                Last 7 days
              </SelectItem>
              <SelectItem value="30d" className="rounded-lg">
                Last 30 days
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillMetric" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={`var(--color-${selectedMetric})`}
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor={`var(--color-${selectedMetric})`}
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => {
                if (selectedMetric === "voltage") return `${value}V`
                if (selectedMetric === "power") return `${value}W`
                if (selectedMetric === "current") return `${value}A`
                return value
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey={selectedMetric}
              type="natural"
              fill="url(#fillMetric)"
              stroke={`var(--color-${selectedMetric})`}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
