import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import AppLayout from "./layouts/AppLayout";
import Dashboard from "./pages/Dashboard";
import DevicesPage from "./pages/DevicesPage";
import AnalyticsPage from "./pages/AnalyticsPage";
import { useSocket } from "./hooks/useSocket";

const queryClient = new QueryClient();

function App() {
  useSocket();

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AppLayout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/devices" element={<DevicesPage />} />
            <Route path="/analytics" element={<AnalyticsPage />} />
            <Route path="/assets" element={<div className="p-6"><h1 className="text-2xl font-bold">Assets Page</h1><p>Coming soon...</p></div>} />
            <Route path="/customers" element={<div className="p-6"><h1 className="text-2xl font-bold">Customers Page</h1><p>Coming soon...</p></div>} />
            <Route path="/settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings Page</h1><p>Coming soon...</p></div>} />
          </Routes>
        </AppLayout>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
