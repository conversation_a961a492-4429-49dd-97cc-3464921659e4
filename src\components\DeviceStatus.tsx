import { Badge } from "../components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { Activity, Wifi, CheckCircle, AlertTriangle } from "lucide-react";

interface DeviceStatusProps {
  deviceName: string;
  isOnline?: boolean;
  lastUpdate?: string;
}

export function DeviceStatus({ deviceName, isOnline = true, lastUpdate = "Just now" }: DeviceStatusProps) {
  return (
    <Card className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-green-50/50 to-blue-50/50" />
      <CardHeader className="relative">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              Device Status
            </CardTitle>
            <CardDescription>{deviceName}</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {isOnline ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <Badge variant="default" className="bg-green-600">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Online
                </Badge>
              </>
            ) : (
              <>
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <Badge variant="destructive">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Offline
                </Badge>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="relative">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Connection Status:</span>
            <span className={`text-sm font-medium ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
              {isOnline ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Last Update:</span>
            <span className="text-sm font-medium">{lastUpdate}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Data Quality:</span>
            <Badge variant="outline" className="text-xs">
              Excellent
            </Badge>
          </div>
          
          {/* Connection Quality Indicator */}
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Signal Strength:</span>
              <span className="text-sm font-medium">95%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: '95%' }}
              />
            </div>
          </div>

          {/* Live indicator */}
          {isOnline && (
            <div className="flex items-center gap-2 pt-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-xs text-muted-foreground">Live data streaming</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
