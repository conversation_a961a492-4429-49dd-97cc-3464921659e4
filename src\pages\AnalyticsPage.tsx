import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "../components/ui/tabs";
import { BarChart3, TrendingUp, Activity, Zap, Download } from "lucide-react";
import { DemoIoTChart } from "../components/charts/DemoIoTChart";
import { useTranslation } from "../hooks/useTranslation";

export default function AnalyticsPage() {
  const { t } = useTranslation();

  return (
    <div className="flex flex-1 flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("analytics.title")}</h1>
          <p className="text-muted-foreground">
            {t("analytics.subtitle")}
          </p>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          {t("analytics.exportReport")}
        </Button>
      </div>

      {/* Analytics Summary */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("analytics.summary.energyConsumption")}</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{t("analytics.summary.values.energyConsumption")}</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="default" className="bg-green-600">{t("analytics.summary.badges.increase", { percent: "12.5" })}</Badge> {t("analytics.summary.badges.fromLastMonth")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("analytics.summary.efficiency")}</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{t("analytics.summary.values.efficiency")}</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="secondary">{t("analytics.summary.badges.increase", { percent: "2.1" })}</Badge> {t("analytics.summary.badges.improved")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("analytics.summary.peakDemand")}</CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">445W</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="outline">Today at 2:30 PM</Badge>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("analytics.summary.costSavings")}</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{t("analytics.summary.values.costSavings")}</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="default">{t("analytics.summary.badges.thisQuarter")}</Badge>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="power" className="space-y-4">
        <TabsList>
          <TabsTrigger value="power">{t("analytics.tabs.powerAnalytics")}</TabsTrigger>
          <TabsTrigger value="efficiency">{t("analytics.tabs.efficiency")}</TabsTrigger>
          <TabsTrigger value="environmental">{t("analytics.tabs.environmental")}</TabsTrigger>
          <TabsTrigger value="predictive">{t("analytics.tabs.predictive")}</TabsTrigger>
        </TabsList>

        <TabsContent value="power" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>{t("analytics.charts.powerConsumptionTrends")}</CardTitle>
                <CardDescription>
                  {t("analytics.charts.monitorPowerUsagePatterns")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DemoIoTChart deviceName="auo-display" />
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Peak Usage Analysis</CardTitle>
                <CardDescription>
                  Identify peak consumption periods
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2 border-b">
                    <div>
                      <div className="font-medium">Morning Peak</div>
                      <div className="text-sm text-muted-foreground">8:00 AM - 10:00 AM</div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono">425W</div>
                      <div className="text-sm text-muted-foreground">Average</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between py-2 border-b">
                    <div>
                      <div className="font-medium">Afternoon Peak</div>
                      <div className="text-sm text-muted-foreground">2:00 PM - 4:00 PM</div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono">445W</div>
                      <div className="text-sm text-muted-foreground">Average</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <div>
                      <div className="font-medium">Evening Peak</div>
                      <div className="text-sm text-muted-foreground">6:00 PM - 8:00 PM</div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono">380W</div>
                      <div className="text-sm text-muted-foreground">Average</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="efficiency" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.charts.efficiencyMetrics")}</CardTitle>
                <CardDescription>{t("analytics.content.efficiency.performanceIndicators")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Power Factor</span>
                    <Badge variant="default">0.97</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Load Factor</span>
                    <Badge variant="secondary">0.85</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Demand Factor</span>
                    <Badge variant="outline">0.78</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.content.efficiency.optimizationSuggestions")}</CardTitle>
                <CardDescription>{t("dashboard.insights.subtitle")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">Schedule Optimization</div>
                    <div className="text-sm text-blue-700">Shift 15% of load to off-peak hours</div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="font-medium text-green-900">Power Factor Improvement</div>
                    <div className="text-sm text-green-700">Install capacitor bank for 3% efficiency gain</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="environmental" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.content.environmental.carbonFootprint")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.2 tons CO₂</div>
                <p className="text-sm text-muted-foreground">This month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Renewable Energy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">67%</div>
                <p className="text-sm text-muted-foreground">From renewables</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Water Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,847L</div>
                <p className="text-sm text-muted-foreground">Cooling systems</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="predictive" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("analytics.charts.predictiveAnalytics")}</CardTitle>
              <CardDescription>
                {t("analytics.charts.forecastFuturePerformance")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-orange-600" />
                    <div className="font-medium text-orange-900">Maintenance Alert</div>
                  </div>
                  <div className="text-sm text-orange-700 mt-1">
                    AUO Display sensor shows 85% likelihood of requiring maintenance within 2 weeks
                  </div>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    <div className="font-medium text-blue-900">Usage Forecast</div>
                  </div>
                  <div className="text-sm text-blue-700 mt-1">
                    Expected 12% increase in power consumption next week due to seasonal patterns
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
