import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { <PERSON><PERSON> } from "../components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "../components/ui/tabs";
import { BarChart3, TrendingUp, Activity, Zap, Download } from "lucide-react";
import { DemoIoTChart } from "../components/charts/DemoIoTChart";

export default function AnalyticsPage() {
  return (
    <div className="flex flex-1 flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Analyze performance trends and insights
          </p>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>

      {/* Analytics Summary */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Energy Consumption</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,847 kWh</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="default" className="bg-green-600">+12.5%</Badge> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Efficiency Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94.2%</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="secondary">+2.1%</Badge> improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Peak Load</CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">445W</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="outline">Today at 2:30 PM</Badge>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$1,247</div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="default">This quarter</Badge>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="power" className="space-y-4">
        <TabsList>
          <TabsTrigger value="power">Power Analytics</TabsTrigger>
          <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
          <TabsTrigger value="environmental">Environmental</TabsTrigger>
          <TabsTrigger value="predictive">Predictive</TabsTrigger>
        </TabsList>

        <TabsContent value="power" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Power Consumption Trends</CardTitle>
                <CardDescription>
                  Monitor power usage patterns over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DemoIoTChart deviceName="auo-display" />
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Peak Usage Analysis</CardTitle>
                <CardDescription>
                  Identify peak consumption periods
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between py-2 border-b">
                    <div>
                      <div className="font-medium">Morning Peak</div>
                      <div className="text-sm text-muted-foreground">8:00 AM - 10:00 AM</div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono">425W</div>
                      <div className="text-sm text-muted-foreground">Average</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between py-2 border-b">
                    <div>
                      <div className="font-medium">Afternoon Peak</div>
                      <div className="text-sm text-muted-foreground">2:00 PM - 4:00 PM</div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono">445W</div>
                      <div className="text-sm text-muted-foreground">Average</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <div>
                      <div className="font-medium">Evening Peak</div>
                      <div className="text-sm text-muted-foreground">6:00 PM - 8:00 PM</div>
                    </div>
                    <div className="text-right">
                      <div className="font-mono">380W</div>
                      <div className="text-sm text-muted-foreground">Average</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="efficiency" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Efficiency Metrics</CardTitle>
                <CardDescription>Current efficiency indicators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Power Factor</span>
                    <Badge variant="default">0.97</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Load Factor</span>
                    <Badge variant="secondary">0.85</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Demand Factor</span>
                    <Badge variant="outline">0.78</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Optimization Recommendations</CardTitle>
                <CardDescription>AI-powered suggestions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">Schedule Optimization</div>
                    <div className="text-sm text-blue-700">Shift 15% of load to off-peak hours</div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="font-medium text-green-900">Power Factor Improvement</div>
                    <div className="text-sm text-green-700">Install capacitor bank for 3% efficiency gain</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="environmental" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Carbon Footprint</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.2 tons CO₂</div>
                <p className="text-sm text-muted-foreground">This month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Renewable Energy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">67%</div>
                <p className="text-sm text-muted-foreground">From renewables</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Water Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,847L</div>
                <p className="text-sm text-muted-foreground">Cooling systems</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="predictive" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Predictive Analytics</CardTitle>
              <CardDescription>
                Machine learning insights and forecasts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-orange-600" />
                    <div className="font-medium text-orange-900">Maintenance Alert</div>
                  </div>
                  <div className="text-sm text-orange-700 mt-1">
                    AUO Display sensor shows 85% likelihood of requiring maintenance within 2 weeks
                  </div>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    <div className="font-medium text-blue-900">Usage Forecast</div>
                  </div>
                  <div className="text-sm text-blue-700 mt-1">
                    Expected 12% increase in power consumption next week due to seasonal patterns
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
