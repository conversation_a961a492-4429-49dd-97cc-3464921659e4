import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

export const TestI18n: React.FC = () => {
  const { t, changeLanguage, currentLanguage } = useTranslation();

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>i18n Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p><strong>Current Language:</strong> {currentLanguage}</p>
        </div>
        
        <div className="space-y-2">
          <p><strong>Translations:</strong></p>
          <ul className="space-y-1 text-sm">
            <li>Dashboard: {t('navigation.dashboard')}</li>
            <li>Devices: {t('navigation.devices')}</li>
            <li>Analytics: {t('navigation.analytics')}</li>
            <li>Loading: {t('common.loading')}</li>
            <li>Online: {t('common.online')}</li>
          </ul>
        </div>
        
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant={currentLanguage === 'en' ? 'default' : 'outline'}
            onClick={() => changeLanguage('en')}
          >
            English
          </Button>
          <Button 
            size="sm" 
            variant={currentLanguage === 'vi' ? 'default' : 'outline'}
            onClick={() => changeLanguage('vi')}
          >
            Tiếng Việt
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
