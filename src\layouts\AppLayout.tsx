import { useLocation, <PERSON> } from "react-router-dom"
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../components/ui/breadcrumb"
import {
  LayoutDashboard,
  Cpu,
  BarChart3,
  Database,
  Users,
  Settings,
  Activity,
  Bell,
  Shield,
  HelpCircle,
  Search,
  Wifi,
  ChevronRight
} from "lucide-react"
import { useTranslation } from "../hooks/useTranslation"
import { LanguageSwitcher } from "../components/LanguageSwitcher"

interface AppLayoutProps {
  children: React.ReactNode
}

const getPageTitle = (pathname: string, t: (key: string) => string): string => {
  switch (pathname) {
    case '/dashboard':
      return t('navigation.dashboard')
    case '/devices':
      return t('navigation.devices')
    case '/analytics':
      return t('navigation.analytics')
    case '/assets':
      return t('navigation.assets')
    case '/customers':
      return t('navigation.customers')
    case '/settings':
      return t('navigation.settings')
    default:
      return t('navigation.dashboard')
  }
}

export default function AppLayout({ children }: AppLayoutProps) {
  const location = useLocation()
  const { t } = useTranslation()
  const pageTitle = getPageTitle(location.pathname, t)

  const navItems: Array<{
    title: string;
    url: string;
    icon: any;
    isActive: boolean;
    badge?: string;
  }> = [
    { title: t("navigation.dashboard"), url: "/dashboard", icon: LayoutDashboard, isActive: location.pathname === "/dashboard" },
    { title: t("navigation.devices"), url: "/devices", icon: Cpu, isActive: location.pathname === "/devices", badge: "4" },
    { title: t("navigation.analytics"), url: "/analytics", icon: BarChart3, isActive: location.pathname === "/analytics" },
    { title: t("navigation.assets"), url: "/assets", icon: Database, isActive: location.pathname === "/assets" },
    { title: t("navigation.customers"), url: "/customers", icon: Users, isActive: location.pathname === "/customers" },
  ];

  const secondaryItems = [
    { title: t("navigation.settings"), url: "/settings", icon: Settings },
    { title: t("navigation.help"), url: "/help", icon: HelpCircle },
    { title: t("navigation.search"), url: "/search", icon: Search },
  ];

  return (
    <div className="flex min-h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 bg-sidebar border-r border-sidebar-border flex-shrink-0 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-sidebar-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wifi className="h-6 w-6 text-sidebar-primary" />
              <span className="text-lg font-semibold text-sidebar-foreground">{t("header.platformName")}</span>
            </div>
            <LanguageSwitcher />
          </div>
        </div>

        {/* Device Switcher */}
        <div className="p-4 border-b border-sidebar-border">
          <div className="flex items-center justify-between p-3 bg-sidebar-accent rounded-lg hover:bg-sidebar-accent/80 transition-colors cursor-pointer">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Activity className="h-4 w-4 text-sidebar-accent-foreground" />
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full"></div>
              </div>
              <div>
                <span className="text-sm font-medium text-sidebar-accent-foreground block">{t("header.deviceSwitcher")}</span>
                <span className="text-xs text-sidebar-accent-foreground/60">{t("common.online")} • {t("header.lastSeen", { time: "2 min" })}</span>
              </div>
            </div>
            <ChevronRight className="h-4 w-4 text-sidebar-accent-foreground" />
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map((item) => (
            <Link
              key={item.url}
              to={item.url}
              className={`sidebar-nav-item flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                item.isActive
                  ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-sm"
                  : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              }`}
            >
              <item.icon className="h-4 w-4" />
              <span className="flex-1">{item.title}</span>
              {item.badge && (
                <span className="bg-sidebar-primary text-sidebar-primary-foreground text-xs px-1.5 py-0.5 rounded-full">
                  {item.badge}
                </span>
              )}
            </Link>
          ))}
        </nav>

        {/* Secondary Navigation */}
        <div className="p-4 border-t border-sidebar-border space-y-2">
          {secondaryItems.map((item) => (
            <Link
              key={item.url}
              to={item.url}
              className="flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors"
            >
              <item.icon className="h-4 w-4" />
              {item.title}
            </Link>
          ))}
        </div>

        {/* User Section */}
        <div className="p-4 border-t border-sidebar-border">
          <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-sidebar-accent transition-colors">
            <div className="h-8 w-8 rounded-full bg-sidebar-primary flex items-center justify-center">
              <span className="text-xs font-medium text-sidebar-primary-foreground">IA</span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-sidebar-foreground">{t("header.user.name")}</p>
              <p className="text-xs text-sidebar-foreground/60 truncate">{t("header.user.email")}</p>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-2 px-4 sm:px-6 lg:px-8">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/">
                    {t("navigation.deviceSelection")}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>{pageTitle}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-muted/20">
          <div className="h-full px-4 py-6 sm:px-6 lg:px-8">
            <div className="mx-auto h-full max-w-7xl">
              {children}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
