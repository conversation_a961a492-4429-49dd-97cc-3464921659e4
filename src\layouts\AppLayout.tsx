import { useLocation } from "react-router-dom"
import {
  <PERSON>readcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../components/ui/breadcrumb"

interface AppLayoutProps {
  children: React.ReactNode
}

const getPageTitle = (pathname: string) => {
  switch (pathname) {
    case '/':
      return 'Dashboard'
    case '/devices':
      return 'Devices'
    case '/analytics':
      return 'Analytics'
    case '/assets':
      return 'Assets'
    case '/customers':
      return 'Customers'
    case '/settings':
      return 'Settings'
    default:
      return 'Page'
  }
}

export default function AppLayout({ children }: AppLayoutProps) {
  const location = useLocation()
  const pageTitle = getPageTitle(location.pathname)

  console.log('AppLayout rendering...');

  return (
    <div className="flex min-h-screen bg-red-100">
      {/* DEBUG: Very visible sidebar */}
      <div className="w-64 bg-red-500 text-white p-4 flex-shrink-0 border-4 border-yellow-400">
        <h2 className="text-lg font-bold mb-4 bg-blue-500 p-2">DEBUG SIDEBAR</h2>
        <div className="bg-green-500 p-2 mb-2">This should be visible!</div>
        <nav className="space-y-2">
          <a href="/" className="block py-2 px-3 rounded bg-gray-800">Dashboard</a>
          <a href="/devices" className="block py-2 px-3 rounded hover:bg-gray-800">Devices</a>
          <a href="/analytics" className="block py-2 px-3 rounded hover:bg-gray-800">Analytics</a>
        </nav>
      </div>
      <main className="flex-1 flex flex-col overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-2 px-4 sm:px-6 lg:px-8">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/">
                    IoMT Platform
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>{pageTitle}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex-1 overflow-auto bg-muted/20">
          <div className="h-full px-4 py-6 sm:px-6 lg:px-8">
            <div className="mx-auto h-full max-w-7xl">
              {children}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
