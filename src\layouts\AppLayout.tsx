import { useLoc<PERSON>, <PERSON> } from "react-router-dom"
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../components/ui/breadcrumb"
import {
  LayoutDashboard,
  Cpu,
  BarChart3,
  Database,
  Users,
  Settings,
  Activity,
  Bell,
  Shield,
  HelpCircle,
  Search,
  Wifi,
  ChevronRight
} from "lucide-react"
import { useTranslation } from "../hooks/useTranslation"
import { LanguageSwitcher } from "../components/LanguageSwitcher"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "../components/ui/sidebar"
import { AppSidebar } from "../components/app-sidebar"

interface AppLayoutProps {
  children: React.ReactNode
}

const getPageTitle = (pathname: string, t: (key: string) => string): string => {
  switch (pathname) {
    case '/':
      return t('navigation.dashboard')
    case '/devices':
      return t('navigation.devices')
    case '/analytics':
      return t('navigation.analytics')
    case '/assets':
      return t('navigation.assets')
    case '/customers':
      return t('navigation.customers')
    case '/settings':
      return t('navigation.settings')
    default:
      return t('navigation.dashboard')
  }
}

export default function AppLayout({ children }: AppLayoutProps) {
  const location = useLocation()
  const { t } = useTranslation()
  const pageTitle = getPageTitle(location.pathname, t)

  const navItems: Array<{
    title: string;
    url: string;
    icon: any;
    isActive: boolean;
    badge?: string;
  }> = [
    { title: t("navigation.dashboard"), url: "/", icon: LayoutDashboard, isActive: location.pathname === "/" },
    { title: t("navigation.devices"), url: "/devices", icon: Cpu, isActive: location.pathname === "/devices", badge: "3" },
    { title: t("navigation.analytics"), url: "/analytics", icon: BarChart3, isActive: location.pathname === "/analytics" },
    { title: t("navigation.assets"), url: "/assets", icon: Database, isActive: location.pathname === "/assets" },
    { title: t("navigation.customers"), url: "/customers", icon: Users, isActive: location.pathname === "/customers" },
  ];

  const secondaryItems = [
    { title: t("navigation.settings"), url: "/settings", icon: Settings },
    { title: t("navigation.help"), url: "/help", icon: HelpCircle },
    { title: t("navigation.search"), url: "/search", icon: Search },
  ];

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center gap-2 px-4 sm:px-6 lg:px-8">
            <SidebarTrigger className="-ml-1" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/">
                    {t("header.platformName")}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>{pageTitle}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-muted/20">
          <div className="h-full px-4 py-6 sm:px-6 lg:px-8">
            <div className="mx-auto h-full max-w-7xl">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
