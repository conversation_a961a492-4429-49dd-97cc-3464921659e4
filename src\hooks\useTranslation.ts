import { useTranslation as useI18nTranslation } from 'react-i18next';

export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
  };

  const getCurrentLanguage = () => {
    return i18n.language;
  };

  const isVietnamese = () => {
    return i18n.language === 'vi';
  };

  const isEnglish = () => {
    return i18n.language === 'en';
  };

  return {
    t,
    changeLanguage,
    getCurrentLanguage,
    isVietnamese,
    isEnglish,
    currentLanguage: i18n.language,
  };
};
