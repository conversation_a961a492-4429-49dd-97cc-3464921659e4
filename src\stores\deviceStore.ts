import { create } from 'zustand';
import { DeviceType, DEVICE_INFO, IoTData } from '../services/iotService';

export interface Device {
  id: DeviceType;
  name: string;
  type: string;
  status: 'online' | 'offline' | 'warning';
  location: string;
  lastSeen: string;
  icon: string;
  metrics: {
    voltage?: number;
    current?: number;
    power_operating?: number;
    frequency?: number;
    energy?: number;
    power_factor?: number;
    operating_time?: string;
  };
  latestData?: IoTData;
}

interface DeviceStore {
  devices: Device[];
  currentDevice: Device | null;
  setCurrentDevice: (device: Device) => void;
  updateDeviceData: (deviceId: DeviceType, data: IoTData) => void;
  initializeDevices: () => void;
}

// Create devices from API device types
const createDevicesFromAPI = (): Device[] => {
  return Object.entries(DEVICE_INFO).map(([id, info]) => ({
    id: id as DeviceType,
    name: info.name,
    type: info.type,
    icon: info.icon,
    status: 'online' as const,
    location: 'IoMT Lab - Floor 1',
    lastSeen: 'Just now',
    metrics: {}
  }));
};

export const useDeviceStore = create<DeviceStore>((set, get) => ({
  devices: [],
  currentDevice: null,
  setCurrentDevice: (device) => set({ currentDevice: device }),
  updateDeviceData: (deviceId, data) => {
    const { devices } = get();
    const updatedDevices = devices.map(device => {
      if (device.id === deviceId) {
        return {
          ...device,
          status: 'online' as const,
          lastSeen: 'Just now',
          latestData: data,
          metrics: {
            voltage: data.voltage,
            current: data.current,
            power_operating: data.power_operating,
            frequency: data.frequency,
            energy: data.energy,
            power_factor: data.power_factor,
            operating_time: data.operating_time
          }
        };
      }
      return device;
    });

    set({ devices: updatedDevices });

    // Update current device if it's the one being updated
    const { currentDevice } = get();
    if (currentDevice?.id === deviceId) {
      const updatedCurrentDevice = updatedDevices.find(d => d.id === deviceId);
      if (updatedCurrentDevice) {
        set({ currentDevice: updatedCurrentDevice });
      }
    }
  },
  initializeDevices: () => {
    const devices = createDevicesFromAPI();
    set({
      devices,
      currentDevice: devices[0] // Default to auo-display
    });
  },
}));
