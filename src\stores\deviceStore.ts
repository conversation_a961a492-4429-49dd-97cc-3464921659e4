import { create } from 'zustand';

export interface Device {
  id: string;
  name: string;
  type: string;
  status: 'online' | 'offline' | 'warning';
  location: string;
  lastSeen: string;
  metrics: {
    voltage?: number;
    current?: number;
    power_operating?: number;
    frequency?: number;
    temperature?: number;
    humidity?: number;
    pressure?: number;
  };
}

interface DeviceStore {
  devices: Device[];
  currentDevice: Device | null;
  setCurrentDevice: (device: Device) => void;
  initializeDevices: () => void;
}

const mockDevices: Device[] = [
  {
    id: 'auo-display',
    name: 'AUO Display Sensor',
    type: 'Power Monitor',
    status: 'online',
    location: 'Building A - Floor 2',
    lastSeen: '2 minutes ago',
    metrics: {
      voltage: 220.5,
      current: 1.1,
      power_operating: 242.5,
      frequency: 50.0,
      temperature: 45
    }
  },
  {
    id: 'env-sensor-001',
    name: 'Environmental Sensor 001',
    type: 'Climate Monitor',
    status: 'online',
    location: 'Building A - Floor 1',
    lastSeen: '1 minute ago',
    metrics: {
      temperature: 24,
      humidity: 65,
      pressure: 1013,
      voltage: 12.5,
      current: 0.2
    }
  },
  {
    id: 'vibration-003',
    name: 'Vibration Sensor 003',
    type: 'Machine Monitor',
    status: 'offline',
    location: 'Factory Floor',
    lastSeen: '2 hours ago',
    metrics: {
      temperature: 52,
      voltage: 0,
      current: 0,
      power_operating: 0
    }
  },
  {
    id: 'gateway-001',
    name: 'Smart Gateway 001',
    type: 'Network Gateway',
    status: 'online',
    location: 'Server Room',
    lastSeen: '30 seconds ago',
    metrics: {
      voltage: 24.0,
      current: 2.1,
      power_operating: 50.4,
      temperature: 35
    }
  }
];

export const useDeviceStore = create<DeviceStore>((set) => ({
  devices: [],
  currentDevice: null,
  setCurrentDevice: (device) => set({ currentDevice: device }),
  initializeDevices: () => set({ 
    devices: mockDevices, 
    currentDevice: mockDevices[0] 
  }),
}));
