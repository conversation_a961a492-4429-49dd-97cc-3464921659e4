import type { ReactNode } from "react";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import { Menu, LayoutDashboard, Settings } from "lucide-react";

type DashboardLayoutProps = {
  children: ReactNode;
};

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r shadow-sm">
        <div className="p-4 border-b flex items-center justify-between">
          <span className="font-bold text-lg">IoMT Admin</span>
          <Menu className="h-5 w-5 text-gray-600" />
        </div>
        <nav className="p-4 space-y-2">
          <button className="flex items-center space-x-2 text-gray-700 hover:text-blue-500">
            <LayoutDashboard className="w-5 h-5" />
            <span>Dashboard</span>
          </button>
          <button className="flex items-center space-x-2 text-gray-700 hover:text-blue-500">
            <Settings className="w-5 h-5" />
            <span>Settings</span>
          </button>
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-6 overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <Button>Gửi dữ liệu</Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <p className="text-gray-600">Điện áp</p>
              <p className="text-xl font-semibold">120V</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <p className="text-gray-600">Dòng điện</p>
              <p className="text-xl font-semibold">1.1A</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <p className="text-gray-600">Công suất</p>
              <p className="text-xl font-semibold">200W</p>
            </CardContent>
          </Card>
        </div>
        {children}
      </main>
    </div>
  );
};

export default DashboardLayout;
