{"common": {"loading": "<PERSON><PERSON> tả<PERSON>...", "error": "Lỗi", "success": "<PERSON><PERSON><PERSON><PERSON> công", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "add": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON> đó", "close": "Đ<PERSON><PERSON>", "open": "Mở", "online": "<PERSON><PERSON><PERSON><PERSON>", "offline": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "connected": "<PERSON><PERSON> kết nối", "disconnected": "<PERSON><PERSON><PERSON> k<PERSON>"}, "navigation": {"dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "devices": "<PERSON><PERSON><PERSON><PERSON> bị", "analytics": "<PERSON><PERSON> tích", "assets": "<PERSON><PERSON><PERSON>", "customers": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Cài đặt", "help": "<PERSON><PERSON><PERSON> g<PERSON>", "search": "<PERSON><PERSON><PERSON>", "deviceSelection": "<PERSON><PERSON><PERSON> th<PERSON> bị"}, "header": {"platformName": "Nền tảng IoMT", "deviceSwitcher": "<PERSON><PERSON><PERSON> biến hiển thị AUG", "lastSeen": "{{time}} tr<PERSON><PERSON><PERSON>", "user": {"name": "Q<PERSON>ản trị IoT", "email": "<EMAIL>"}}, "dashboard": {"title": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "subtitle": "<PERSON><PERSON><PERSON><PERSON> sát thiết bị: {{deviceName}}", "simulateData": "<PERSON><PERSON> phỏng dữ liệu", "metrics": {"voltage": "<PERSON><PERSON><PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON> đi<PERSON>n", "power": "<PERSON><PERSON><PERSON>", "frequency": "<PERSON><PERSON><PERSON> s<PERSON>", "normal": "<PERSON><PERSON><PERSON>", "stable": "Ổn định", "optimal": "<PERSON><PERSON><PERSON>", "increasing": "t<PERSON>ng", "decreasing": "<PERSON><PERSON><PERSON><PERSON>", "loadDemandRising": "<PERSON><PERSON> cầu tải đang tăng đều", "powerConsumptionUp": "<PERSON><PERSON><PERSON><PERSON> thụ điện năng tăng", "operatingAtCapacity": "Hoạt động ở {{percent}}% công suất", "perfectFrequencyMatch": "<PERSON><PERSON>n số khớp hoàn h<PERSON>o", "gridSynchronizationOptimal": "<PERSON><PERSON><PERSON> bộ lưới điện tối <PERSON>u"}, "status": {"deviceStatus": "<PERSON><PERSON><PERSON><PERSON> thái thiết bị", "connectionStatus": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "lastUpdate": "<PERSON><PERSON><PERSON> nh<PERSON> cu<PERSON>", "dataQuality": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> dữ liệu", "signalStrength": "<PERSON>ư<PERSON>ng độ tín hiệu", "excellent": "<PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON><PERSON>", "fair": "Khá", "poor": "<PERSON><PERSON><PERSON>"}, "charts": {"powerConsumption": "<PERSON><PERSON><PERSON><PERSON> thụ điện năng", "realTimePowerConsumptionMonitoring": "<PERSON><PERSON><PERSON><PERSON> sát tiêu thụ điện năng thời gian thực", "iotMetrics": "Chỉ số IoT - tự động hiển thị", "deviceData": "<PERSON><PERSON> liệu thiết bị", "last30Days": "30 ngày qua", "voltage": "<PERSON><PERSON><PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON> đi<PERSON>n"}, "insights": {"title": "Th<PERSON>ng tin chi tiết & <PERSON><PERSON> xuất AI", "subtitle": "<PERSON>ân tích và gợi ý được hỗ trợ bởi học máy", "powerFactorOptimization": "<PERSON><PERSON><PERSON>u hóa hệ số công suất", "maintenanceForecast": "<PERSON><PERSON> báo bảo trì", "peakLoadWarning": "<PERSON><PERSON><PERSON> b<PERSON>o tải cao điểm", "energyEfficiencyTip": "Mẹo tiết kiệm năng lượng", "highImpact": "<PERSON><PERSON><PERSON> cao", "mediumImpact": "<PERSON><PERSON><PERSON> động trung bình", "lowImpact": "<PERSON><PERSON><PERSON> đ<PERSON>ng thấp", "confidence": "<PERSON><PERSON> tin cậy {{percent}}%", "viewDetails": "<PERSON>em chi tiết", "implement": "<PERSON><PERSON><PERSON><PERSON>", "dismiss": "Bỏ qua", "descriptions": {"powerFactorOptimization": "Lắp đặt ngân hàng tụ điện có thể cải thiện hệ số công suất lên 0.99, gi<PERSON><PERSON> chi phí năng lượ<PERSON> 8%", "maintenanceForecast": "Dựa trên các mẫu hiện tại, thi<PERSON><PERSON> bị có thể cần bảo trì trong 14 ngày", "peakLoadWarning": "<PERSON><PERSON> tiến gần đến công suất tối đa trong khung giờ 2-4 chi<PERSON>u", "energyEfficiencyTip": "Điều chỉnh lịch hoạt động có thể giảm nhu cầu cao điểm 15%"}}, "recentData": {"title": "<PERSON><PERSON> liệu gần đây", "subtitle": "<PERSON><PERSON><PERSON> số đo mới nhất từ {{deviceName}}", "reading": "Số đo #{{number}}", "invalidData": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ"}}, "devices": {"title": "<PERSON><PERSON><PERSON><PERSON> bị", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý và giám sát các thiết bị IoT của bạn", "addDevice": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON> bị", "totalDevices": "<PERSON><PERSON><PERSON> số thiết bị", "allConnected": "Tất cả đã kết nối", "onlineDevices": "<PERSON><PERSON><PERSON><PERSON> bị trực tuyến", "offlineDevices": "<PERSON><PERSON><PERSON><PERSON> bị ngo<PERSON>i tuyến", "alertsActive": "<PERSON><PERSON><PERSON> báo đang hoạt động", "deviceTypes": {"powerMonitor": "<PERSON><PERSON><PERSON><PERSON> sát điện năng", "climateMonitor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t kh<PERSON> hậu", "machineMonitor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t máy móc", "networkGateway": "<PERSON><PERSON><PERSON> mạng"}, "status": {"online": "<PERSON><PERSON><PERSON><PERSON>", "offline": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "lastSeen": "<PERSON><PERSON><PERSON> cu<PERSON>i thấy", "location": "<PERSON><PERSON> trí"}, "metrics": {"voltage": "<PERSON><PERSON><PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON> đi<PERSON>n", "power": "<PERSON><PERSON><PERSON>", "temperature": "Nhiệt độ", "humidity": "<PERSON><PERSON> <PERSON><PERSON>", "vibration": "<PERSON><PERSON> đ<PERSON>", "uptime": "<PERSON><PERSON><PERSON><PERSON> gian ho<PERSON>t động", "connectedDevices": "<PERSON><PERSON><PERSON><PERSON> bị đã kết nối", "networkStatus": "<PERSON>r<PERSON><PERSON> thái mạng", "memoryUsage": "Sử dụng bộ nhớ"}, "actions": {"viewDetails": "<PERSON>em chi tiết", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "restart": "Khởi động lại"}}, "analytics": {"title": "<PERSON><PERSON> tích", "subtitle": "<PERSON><PERSON> tích xu hướng hiệu suất và thông tin chi tiết", "exportReport": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "summary": {"energyConsumption": "<PERSON><PERSON><PERSON><PERSON> thụ năng l<PERSON>", "fromLastMonth": "từ tháng trước", "peakDemand": "<PERSON><PERSON> cầu cao điểm", "thisWeek": "tu<PERSON><PERSON> n<PERSON>y", "efficiency": "<PERSON><PERSON><PERSON>", "costSavings": "<PERSON><PERSON><PERSON><PERSON> kiệm chi phí", "thisQuarter": "quý n<PERSON>y", "values": {"energyConsumption": "2,847 kWh", "peakDemand": "1.2 MW", "efficiency": "94.2%", "costSavings": "$1,247"}, "badges": {"increase": "+{{percent}}%", "decrease": "-{{percent}}%", "fromLastMonth": "từ tháng trước", "thisWeek": "tu<PERSON><PERSON> n<PERSON>y", "improved": "<PERSON><PERSON><PERSON>", "thisQuarter": "quý n<PERSON>y"}}, "tabs": {"powerAnalytics": "<PERSON><PERSON> tích điện năng", "efficiency": "<PERSON><PERSON><PERSON>", "environmental": "<PERSON>ô<PERSON> trường", "predictive": "<PERSON><PERSON> đo<PERSON>"}, "charts": {"powerConsumptionTrends": "<PERSON> hướng tiêu thụ điện năng", "monitorPowerUsagePatterns": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t các mẫu sử dụng điện theo thời gian", "efficiencyMetrics": "Chỉ số hiệu suất", "trackSystemPerformance": "<PERSON>õi hiệu su<PERSON>t hệ thống và cơ hội tối ưu hóa", "environmentalImpact": "<PERSON><PERSON><PERSON> động môi trường", "carbonFootprintAnalysis": "<PERSON><PERSON> tích dấu chân carbon và chỉ số bền vững", "predictiveAnalytics": "<PERSON><PERSON> tích dự đoán", "forecastFuturePerformance": "<PERSON><PERSON> báo hiệu suất tương lai và nhu cầu bảo trì", "realTimeMonitoring": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực", "liveDataStreaming": "<PERSON><PERSON><PERSON>ền dữ liệu trực tiếp và cảnh báo tức thì"}, "content": {"powerAnalytics": {"description": "<PERSON><PERSON> tích toàn diện về tiêu thụ điện năng và xu hướng", "realTimeMonitoring": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực", "liveDataDescription": "<PERSON><PERSON><PERSON>ền dữ liệu trực tiếp và cảnh báo tức thì"}, "efficiency": {"description": "Chỉ số hiệu suất hệ thống và cơ hội tối ưu hóa", "performanceIndicators": "Chỉ số hiệu suất", "optimizationSuggestions": "<PERSON><PERSON> xuất tối ưu hóa"}, "environmental": {"description": "<PERSON><PERSON><PERSON> động môi trường và chỉ số bền vững", "carbonFootprint": "<PERSON><PERSON>u chân carbon", "sustainabilityScore": "<PERSON><PERSON><PERSON><PERSON> bền vững"}, "predictive": {"description": "<PERSON><PERSON> tích dự đoán và dự báo bảo trì", "maintenanceSchedule": "<PERSON><PERSON><PERSON> b<PERSON> trì", "performanceForecast": "<PERSON><PERSON> báo hiệu su<PERSON>t"}}}, "components": {"deviceStatus": {"title": "<PERSON><PERSON><PERSON><PERSON> thái thiết bị", "connectionStatus": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "lastUpdate": "<PERSON><PERSON><PERSON> nh<PERSON> cu<PERSON>", "dataQuality": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> dữ liệu", "signalStrength": "<PERSON>ư<PERSON>ng độ tín hiệu", "connected": "<PERSON><PERSON> kết nối", "disconnected": "<PERSON><PERSON><PERSON> k<PERSON>", "excellent": "<PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON><PERSON>", "fair": "Khá", "poor": "<PERSON><PERSON><PERSON>", "liveIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON> dữ liệu trực tiếp"}, "aiInsights": {"title": "Th<PERSON>ng tin chi tiết & <PERSON><PERSON> xuất AI", "subtitle": "<PERSON>ân tích và gợi ý được hỗ trợ bởi học máy"}}, "actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "viewDetails": "<PERSON>em chi tiết", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "restart": "Khởi động lại", "export": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>"}, "language": {"english": "English", "vietnamese": "Tiếng <PERSON>", "switchLanguage": "<PERSON><PERSON><PERSON><PERSON> ngôn ngữ"}}