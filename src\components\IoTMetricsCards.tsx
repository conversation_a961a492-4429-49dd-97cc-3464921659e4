import { TrendingUpIcon, Activity, Zap, Gauge } from "lucide-react"

import { Badge } from "../components/ui/badge"
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../components/ui/card"

interface IoTMetricsCardsProps {
  data?: {
    voltage: number
    current: number
    power_operating: number
    frequency: number
    temperature?: number
  }
}

export function IoTMetricsCards({ data }: IoTMetricsCardsProps) {
  const metrics = data || {
    voltage: 220,
    current: 1.1,
    power_operating: 242,
    frequency: 50,
    temperature: 45
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Voltage
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.voltage}V
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-green-50 text-green-700">
              <Activity className="size-3" />
              Normal
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-green-600">
            Stable voltage level <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Within optimal range (200-240V)
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Current
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.current}A
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-blue-50 text-blue-700">
              <TrendingUpIcon className="size-3" />
              +5.2%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-blue-600">
            Current increasing <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Load demand rising steadily
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Power
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.power_operating}W
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-orange-50 text-orange-700">
              <TrendingUpIcon className="size-3" />
              +8.1%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-orange-600">
            Power consumption up <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Operating at 85% capacity
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Gauge className="h-4 w-4" />
            Frequency
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.frequency}Hz
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-green-50 text-green-700">
              <Activity className="size-3" />
              Stable
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-green-600">
            Perfect frequency match <Activity className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Grid synchronization optimal
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
