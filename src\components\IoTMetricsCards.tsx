import { TrendingUpIcon, Activity, Zap, Gauge } from "lucide-react"

import { Badge } from "../components/ui/badge"
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../components/ui/card"
import { useTranslation } from "../hooks/useTranslation"

interface IoTMetricsCardsProps {
  data?: {
    voltage: number
    current: number
    power_operating: number
    frequency: number
    temperature?: number
  }
}

export function IoTMetricsCards({ data }: IoTMetricsCardsProps) {
  const { t } = useTranslation();
  const metrics = data || {
    voltage: 220,
    current: 1.1,
    power_operating: 242,
    frequency: 50,
    temperature: 45
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            {t("dashboard.metrics.voltage")}
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.voltage}V
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-green-50 text-green-700">
              <Activity className="size-3" />
              {t("dashboard.metrics.normal")}
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-green-600">
            Stable voltage level <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Within optimal range (200-240V)
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
{t("dashboard.metrics.current")}
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.current}A
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-blue-50 text-blue-700">
              <TrendingUpIcon className="size-3" />
              +5.2%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-blue-600">
            {t("dashboard.metrics.current")} {t("dashboard.metrics.increasing")} <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {t("dashboard.metrics.loadDemandRising")}
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
{t("dashboard.metrics.power")}
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.power_operating}W
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-orange-50 text-orange-700">
              <TrendingUpIcon className="size-3" />
              +8.1%
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-orange-600">
            {t("dashboard.metrics.powerConsumptionUp")} <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {t("dashboard.metrics.operatingAtCapacity", { percent: 85 })}
          </div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription className="flex items-center gap-2">
            <Gauge className="h-4 w-4" />
{t("dashboard.metrics.frequency")}
          </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {metrics.frequency}Hz
          </CardTitle>
          <div className="absolute right-4 top-4">
            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs bg-green-50 text-green-700">
              <Activity className="size-3" />
{t("dashboard.metrics.stable")}
            </Badge>
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium text-green-600">
            {t("dashboard.metrics.perfectFrequencyMatch")} <Activity className="size-4" />
          </div>
          <div className="text-muted-foreground">
            {t("dashboard.metrics.gridSynchronizationOptimal")}
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
