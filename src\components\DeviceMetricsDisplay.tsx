import React from 'react';
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Clock, Zap, Activity, Gauge } from 'lucide-react';
import { useTranslation } from '../hooks/useTranslation';
import type { IoTData } from '../types/iot';

interface DeviceMetricsDisplayProps {
  data: IoTData[];
  deviceName: string;
  isLoading?: boolean;
}

export const DeviceMetricsDisplay: React.FC<DeviceMetricsDisplayProps> = ({
  data,
  deviceName,
  isLoading = false
}) => {
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {t('dashboard.recentData.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {t('dashboard.recentData.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No data available for {deviceName}
          </div>
        </CardContent>
      </Card>
    );
  }

  const latestData = data[data.length - 1];
  const recentData = data.slice(-5).reverse(); // Show last 5 readings

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 DeviceMetricsDisplay Debug:', {
      dataLength: data.length,
      latestData,
      recentDataSample: recentData[0]
    });
  }

  return (
    <div className="space-y-6">
      {/* Latest Metrics Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            Current Metrics - {deviceName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Voltage</div>
              <div className="text-2xl font-bold">{Number(latestData.voltage || 0).toFixed(1)}V</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Current</div>
              <div className="text-2xl font-bold">{Number(latestData.current || 0).toFixed(2)}A</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Power</div>
              <div className="text-2xl font-bold">{Number(latestData.power_operating || 0).toFixed(1)}W</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Energy</div>
              <div className="text-2xl font-bold">{Number(latestData.energy || 0).toFixed(1)}Wh</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Frequency</div>
              <div className="text-2xl font-bold">{Number(latestData.frequency || 0).toFixed(1)}Hz</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">Power Factor</div>
              <div className="text-2xl font-bold">{Number(latestData.power_factor || 0).toFixed(3)}</div>
            </div>
          </div>
          
          {latestData.operating_time && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                Operating Time: {latestData.operating_time}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Readings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="h-5 w-5 text-green-600" />
            {t('dashboard.recentData.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentData.map((reading, index) => (
              <div
                key={reading.id}
                className={`p-4 rounded-lg border ${
                  index === 0 ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant={index === 0 ? 'default' : 'secondary'}>
                      {t('dashboard.recentData.reading', { number: reading.id })}
                    </Badge>
                    {index === 0 && (
                      <Badge variant="outline" className="text-green-600 border-green-300">
                        Latest
                      </Badge>
                    )}
                  </div>
                  {reading.operating_time && (
                    <div className="text-xs text-muted-foreground">
                      {reading.operating_time}
                    </div>
                  )}
                </div>
                
                <div className="grid grid-cols-3 md:grid-cols-6 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">V:</span>
                    <span className="ml-1 font-mono">{Number(reading.voltage || 0).toFixed(1)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">A:</span>
                    <span className="ml-1 font-mono">{Number(reading.current || 0).toFixed(2)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">W:</span>
                    <span className="ml-1 font-mono">{Number(reading.power_operating || 0).toFixed(1)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Wh:</span>
                    <span className="ml-1 font-mono">{Number(reading.energy || 0).toFixed(1)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Hz:</span>
                    <span className="ml-1 font-mono">{Number(reading.frequency || 0).toFixed(1)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">PF:</span>
                    <span className="ml-1 font-mono">{Number(reading.power_factor || 0).toFixed(3)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
