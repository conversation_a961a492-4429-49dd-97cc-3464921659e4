"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../ui/chart"
import type { ChartConfig } from "../ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "../ui/toggle-group"

const chartConfig = {
  voltage: {
    label: "Voltage (V)",
    color: "hsl(var(--chart-1))",
  },
  power: {
    label: "Power (W)",
    color: "hsl(var(--chart-2))",
  },
  current: {
    label: "Current (A)",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig

// Generate mock IoT data for demo
const generateMockData = () => {
  const data = []
  const now = new Date()
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    
    // Generate realistic IoT values with some variation
    const baseVoltage = 220 + (Math.random() - 0.5) * 10
    const baseCurrent = 1.0 + (Math.random() - 0.5) * 0.4
    const basePower = baseVoltage * baseCurrent + (Math.random() - 0.5) * 50
    
    data.push({
      date: date.toISOString().split('T')[0],
      voltage: Number(baseVoltage.toFixed(1)),
      power: Number(basePower.toFixed(1)),
      current: Number(baseCurrent.toFixed(2)),
      timestamp: date.getTime(),
    })
  }
  
  return data
}

interface DemoIoTChartProps {
  deviceName: string
}

export function DemoIoTChart({ deviceName }: DemoIoTChartProps) {
  const [timeRange, setTimeRange] = React.useState("30d")
  const [selectedMetric, setSelectedMetric] = React.useState<"voltage" | "power" | "current">("voltage")
  
  const mockData = React.useMemo(() => generateMockData(), [])

  const filteredData = React.useMemo(() => {
    const now = new Date()
    let daysToSubtract = 30
    
    if (timeRange === "7d") {
      daysToSubtract = 7
    } else if (timeRange === "24h") {
      daysToSubtract = 1
    }
    
    const startDate = new Date(now)
    startDate.setDate(startDate.getDate() - daysToSubtract)
    
    return mockData.filter((item) => item.timestamp >= startDate.getTime())
  }, [mockData, timeRange])

  return (
    <Card className="@container/card border-green-200 bg-gradient-to-br from-green-50/50 to-blue-50/50">
      <CardHeader className="relative">
        <CardTitle>IoT Metrics - {deviceName}</CardTitle>
        <CardDescription>
          <span className="@[540px]/card:block hidden">
            Demo data showing simulated IoT metrics
          </span>
          <span className="@[540px]/card:hidden">Demo IoT data</span>
        </CardDescription>
        <div className="absolute right-4 top-4 space-y-2">
          <Select value={selectedMetric} onValueChange={(value: "voltage" | "power" | "current") => setSelectedMetric(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="voltage">Voltage</SelectItem>
              <SelectItem value="power">Power</SelectItem>
              <SelectItem value="current">Current</SelectItem>
            </SelectContent>
          </Select>
          
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="@[767px]/card:flex hidden"
          >
            <ToggleGroupItem value="24h" className="h-8 px-2.5">
              24h
            </ToggleGroupItem>
            <ToggleGroupItem value="7d" className="h-8 px-2.5">
              7 days
            </ToggleGroupItem>
            <ToggleGroupItem value="30d" className="h-8 px-2.5">
              30 days
            </ToggleGroupItem>
          </ToggleGroup>
          
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="@[767px]/card:hidden flex w-32"
              aria-label="Select time range"
            >
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="24h" className="rounded-lg">
                Last 24 hours
              </SelectItem>
              <SelectItem value="7d" className="rounded-lg">
                Last 7 days
              </SelectItem>
              <SelectItem value="30d" className="rounded-lg">
                Last 30 days
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillMetric" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={`var(--color-${selectedMetric})`}
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor={`var(--color-${selectedMetric})`}
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => {
                if (selectedMetric === "voltage") return `${value}V`
                if (selectedMetric === "power") return `${value}W`
                if (selectedMetric === "current") return `${value}A`
                return value
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value: any) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey={selectedMetric}
              type="natural"
              fill="url(#fillMetric)"
              stroke={`var(--color-${selectedMetric})`}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
