// IoT Types
export interface IoTData {
  id: number;
  voltage: number;
  current: number;
  power_operating: number;
  energy: number;
  frequency: number;
  power_factor: number;
  operating_time: string; // "hh:mm:ss" format from server
}

export interface IoTDataInput {
  voltage: number;
  current: number;
  power_operating: number;
  energy: number;
  frequency: number;
  power_factor: number;
}

export type DeviceType = 'auo-display' | 'camera-control' | 'electronic' | 'led-nova';

// Device information
export const DEVICE_INFO = {
  'auo-display': {
    name: 'AUG Display Sensor',
    type: 'Power Monitor',
    description: 'Display power monitoring sensor',
    icon: '📺'
  },
  'camera-control': {
    name: 'Camera Control Unit',
    type: 'Control System', 
    description: 'Camera control and monitoring system',
    icon: '📹'
  },
  'electronic': {
    name: 'Electronic Monitor',
    type: 'Electronic System',
    description: 'General electronic device monitoring',
    icon: '⚡'
  },
  'led-nova': {
    name: 'LED Nova Controller',
    type: 'LED Controller',
    description: 'LED display controller and monitor',
    icon: '💡'
  }
} as const;
