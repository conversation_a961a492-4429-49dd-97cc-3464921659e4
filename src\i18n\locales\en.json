{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected"}, "navigation": {"dashboard": "Dashboard", "devices": "Devices", "analytics": "Analytics", "assets": "Assets", "customers": "Customers", "settings": "Settings", "help": "Help", "search": "Search"}, "header": {"platformName": "IoMT Platform", "deviceSwitcher": "AUG Display Sensor", "lastSeen": "{{time}} ago", "user": {"name": "IoT Admin", "email": "<EMAIL>"}}, "dashboard": {"title": "Dashboard", "metrics": {"voltage": "Voltage", "current": "Current", "power": "Power", "frequency": "Frequency", "normal": "Normal", "stable": "Stable", "optimal": "Optimal"}, "status": {"deviceStatus": "Device Status", "connectionStatus": "Connection Status", "lastUpdate": "Last Update", "dataQuality": "Data Quality", "signalStrength": "Signal Strength", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor"}, "charts": {"powerConsumption": "Power Consumption", "realTimePowerConsumptionMonitoring": "Real-time power consumption monitoring", "iotMetrics": "IoT Metrics - auto-display", "deviceData": "Device data", "last30Days": "Last 30 days", "voltage": "Voltage", "current": "Current"}, "insights": {"title": "AI Insights & Recommendations", "subtitle": "Machine learning powered analytics and suggestions", "powerFactorOptimization": "Power Factor Optimization", "highImpact": "High Impact", "confidence": "{{percent}}% confidence"}, "recentData": {"title": "Recent Data", "subtitle": "Latest readings from AUG Display Sensor", "reading": "Reading #{{number}}", "invalidData": "Invalid Data"}}, "devices": {"title": "Devices", "subtitle": "Manage your IoT devices", "addDevice": "Add <PERSON>", "deviceList": "Device List", "deviceDetails": "<PERSON>ce Det<PERSON>", "status": "Status", "lastSeen": "Last Seen", "location": "Location", "type": "Type"}, "analytics": {"title": "Analytics", "subtitle": "Data insights and reports", "overview": "Overview", "reports": "Reports", "trends": "Trends", "predictions": "Predictions"}, "language": {"english": "English", "vietnamese": "Tiếng <PERSON>", "switchLanguage": "Switch Language"}}