{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected"}, "navigation": {"dashboard": "Dashboard", "devices": "Devices", "analytics": "Analytics", "assets": "Assets", "customers": "Customers", "settings": "Settings", "help": "Help", "search": "Search"}, "header": {"platformName": "IoMT Platform", "deviceSwitcher": "AUG Display Sensor", "lastSeen": "{{time}} ago", "user": {"name": "IoT Admin", "email": "<EMAIL>"}}, "dashboard": {"title": "Dashboard", "subtitle": "Monitoring device: {{deviceName}}", "simulateData": "Simulate Data", "metrics": {"voltage": "Voltage", "current": "Current", "power": "Power", "frequency": "Frequency", "normal": "Normal", "stable": "Stable", "optimal": "Optimal", "increasing": "increasing", "decreasing": "decreasing", "loadDemandRising": "Load demand rising steadily", "powerConsumptionUp": "Power consumption up", "operatingAtCapacity": "Operating at {{percent}}% capacity", "perfectFrequencyMatch": "Perfect frequency match", "gridSynchronizationOptimal": "Grid synchronization optimal"}, "status": {"deviceStatus": "Device Status", "connectionStatus": "Connection Status", "lastUpdate": "Last Update", "dataQuality": "Data Quality", "signalStrength": "Signal Strength", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor"}, "charts": {"powerConsumption": "Power Consumption", "realTimePowerConsumptionMonitoring": "Real-time power consumption monitoring", "iotMetrics": "IoT Metrics - auto-display", "deviceData": "Device data", "last30Days": "Last 30 days", "voltage": "Voltage", "current": "Current"}, "insights": {"title": "AI Insights & Recommendations", "subtitle": "Machine learning powered analytics and suggestions", "powerFactorOptimization": "Power Factor Optimization", "maintenanceForecast": "Maintenance Forecast", "peakLoadWarning": "Peak Load Warning", "energyEfficiencyTip": "Energy Efficiency Tip", "highImpact": "High Impact", "mediumImpact": "Medium Impact", "lowImpact": "Low Impact", "confidence": "{{percent}}% confidence", "viewDetails": "View Details", "implement": "Implement", "dismiss": "<PERSON><PERSON><PERSON>", "descriptions": {"powerFactorOptimization": "Installing a capacitor bank could improve power factor to 0.99, reducing energy costs by 8%", "maintenanceForecast": "Based on current patterns, device may require maintenance in 14 days", "peakLoadWarning": "Approaching maximum capacity during 2-4 PM timeframe", "energyEfficiencyTip": "Adjusting operating schedule could reduce peak demand by 15%"}}, "recentData": {"title": "Recent Data", "subtitle": "Latest readings from {{deviceName}}", "reading": "Reading #{{number}}", "invalidData": "Invalid Data"}}, "devices": {"title": "Devices", "subtitle": "Manage and monitor your IoT devices", "addDevice": "Add <PERSON>", "totalDevices": "Total Devices", "allConnected": "All connected", "onlineDevices": "Online Devices", "offlineDevices": "Offline Devices", "alertsActive": "Alerts Active", "deviceTypes": {"powerMonitor": "Power Monitor", "climateMonitor": "Climate Monitor", "machineMonitor": "Machine Monitor", "networkGateway": "Network Gateway"}, "status": {"online": "Online", "offline": "Offline", "lastSeen": "Last seen", "location": "Location"}, "metrics": {"voltage": "Voltage", "current": "Current", "power": "Power", "temperature": "Temperature", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "vibration": "Vibration", "uptime": "Uptime", "connectedDevices": "Connected Devices", "networkStatus": "Network Status", "memoryUsage": "Memory Usage"}, "actions": {"viewDetails": "View Details", "configure": "Configure", "restart": "<PERSON><PERSON>"}}, "analytics": {"title": "Analytics", "subtitle": "Analyze performance trends and insights", "exportReport": "Export Report", "summary": {"energyConsumption": "Energy Consumption", "fromLastMonth": "from last month", "peakDemand": "Peak Demand", "thisWeek": "This week", "efficiency": "Efficiency", "costSavings": "Cost Savings", "thisQuarter": "This quarter", "values": {"energyConsumption": "2,847 kWh", "peakDemand": "1.2 MW", "efficiency": "94.2%", "costSavings": "$1,247"}, "badges": {"increase": "+{{percent}}%", "decrease": "-{{percent}}%", "fromLastMonth": "from last month", "thisWeek": "This week", "improved": "Improved", "thisQuarter": "This quarter"}}, "tabs": {"powerAnalytics": "Power Analytics", "efficiency": "Efficiency", "environmental": "Environmental", "predictive": "Predictive"}, "charts": {"powerConsumptionTrends": "Power Consumption Trends", "monitorPowerUsagePatterns": "Monitor power usage patterns over time", "efficiencyMetrics": "Efficiency Metrics", "trackSystemPerformance": "Track system performance and optimization opportunities", "environmentalImpact": "Environmental Impact", "carbonFootprintAnalysis": "Carbon footprint analysis and sustainability metrics", "predictiveAnalytics": "Predictive Analytics", "forecastFuturePerformance": "Forecast future performance and maintenance needs", "realTimeMonitoring": "Real-time Monitoring", "liveDataStreaming": "Live data streaming and instant alerts"}, "content": {"powerAnalytics": {"description": "Comprehensive power consumption analysis and trends", "realTimeMonitoring": "Real-time Monitoring", "liveDataDescription": "Live data streaming and instant alerts"}, "efficiency": {"description": "System efficiency metrics and optimization opportunities", "performanceIndicators": "Performance Indicators", "optimizationSuggestions": "Optimization Suggestions"}, "environmental": {"description": "Environmental impact and sustainability metrics", "carbonFootprint": "Carbon Footprint", "sustainabilityScore": "Sustainability Score"}, "predictive": {"description": "Predictive analytics and maintenance forecasting", "maintenanceSchedule": "Maintenance Schedule", "performanceForecast": "Performance Forecast"}}}, "components": {"deviceStatus": {"title": "Device Status", "connectionStatus": "Connection Status", "lastUpdate": "Last Update", "dataQuality": "Data Quality", "signalStrength": "Signal Strength", "connected": "Connected", "disconnected": "Disconnected", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor", "liveIndicator": "Live data streaming"}, "aiInsights": {"title": "AI Insights & Recommendations", "subtitle": "Machine learning powered analytics and suggestions"}}, "actions": {"add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "viewDetails": "View Details", "configure": "Configure", "restart": "<PERSON><PERSON>", "export": "Export", "import": "Import", "refresh": "Refresh"}, "language": {"english": "English", "vietnamese": "Tiếng <PERSON>", "switchLanguage": "Switch Language"}}