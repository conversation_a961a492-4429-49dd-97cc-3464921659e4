import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { 
  Activity, 
  Zap, 
  Monitor, 
  Stethoscope, 
  Building2, 
  Plug,
  ArrowRight,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useTranslation } from '../hooks/useTranslation';
import { useDeviceStore } from '../stores/deviceStore';
import type { DeviceType } from '../services/iotService';
import { DEVICE_INFO } from '../services/iotService';

const DeviceSelectionPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { setCurrentDevice, devices, initializeDevices } = useDeviceStore();

  React.useEffect(() => {
    initializeDevices();
  }, [initializeDevices]);

  const handleDeviceSelect = (deviceType: DeviceType) => {
    const selectedDevice = devices.find(d => d.id === deviceType);
    if (selectedDevice) {
      setCurrentDevice(selectedDevice);
      navigate('/dashboard');
    }
  };

  const deviceSlots = [
    {
      id: 'auo-display' as DeviceType,
      slotNumber: 1,
      name: 'Màn hình hiển thị',
      description: 'Thiết bị hiển thị hình ảnh nội soi',
      icon: '📺',
      color: 'blue',
      status: 'online'
    },
    {
      id: 'camera-control' as DeviceType,
      slotNumber: 2,
      name: 'Điều khiển camera',
      description: 'Hệ thống điều khiển camera nội soi',
      icon: '📹',
      color: 'green',
      status: 'online'
    },
    {
      id: 'electronic' as DeviceType,
      slotNumber: 3,
      name: 'Mạch điện tử',
      description: 'Hệ thống điện tử và xử lý tín hiệu',
      icon: '⚡',
      color: 'orange',
      status: 'online'
    },
    {
      id: 'led-nova' as DeviceType,
      slotNumber: 4,
      name: 'Đèn LED Nova',
      description: 'Hệ thống chiếu sáng LED chuyên dụng',
      icon: '💡',
      color: 'purple',
      status: 'online'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-blue-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Stethoscope className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">IoMT Endoscopy System</h1>
                <p className="text-sm text-gray-600">Hệ thống giám sát thiết bị nội soi phòng mổ</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Introduction */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Building2 className="h-4 w-4" />
            Bệnh viện Đa khoa Quốc tế
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Chọn thiết bị cần giám sát
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Hệ thống nội soi phòng mổ của chúng tôi bao gồm 4 ổ cắm thiết bị chính. 
            Vui lòng chọn thiết bị bạn muốn giám sát để xem thông số hoạt động chi tiết.
          </p>
        </div>

        {/* Device Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {deviceSlots.map((device) => (
            <Card
              key={device.id}
              className={`relative overflow-hidden border-2 ${
                device.color === 'blue' ? 'hover:border-blue-300' :
                device.color === 'green' ? 'hover:border-green-300' :
                device.color === 'orange' ? 'hover:border-orange-300' :
                'hover:border-purple-300'
              } transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer group bg-white/70 backdrop-blur-sm`}
              onClick={() => handleDeviceSelect(device.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between mb-2">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-xl ${
                    device.color === 'blue' ? 'bg-blue-100' :
                    device.color === 'green' ? 'bg-green-100' :
                    device.color === 'orange' ? 'bg-orange-100' :
                    'bg-purple-100'
                  } text-2xl`}>
                    {device.icon}
                  </div>
                  <div className="flex items-center gap-1">
                    <CheckCircle className={`h-4 w-4 text-green-500`} />
                    <Badge variant="outline" className="text-green-600 border-green-300 bg-green-50">
                      Online
                    </Badge>
                  </div>
                </div>
                <CardTitle className="text-lg font-bold text-gray-900">
                  Ổ cắm {device.slotNumber}
                </CardTitle>
                <div className="text-sm font-medium text-gray-700">
                  {device.name}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <p className="text-sm text-gray-600 mb-4">
                  {device.description}
                </p>
                
                <div className="flex items-center gap-2 text-xs text-gray-500 mb-4">
                  <Plug className="h-3 w-3" />
                  <span>Kết nối ổn định</span>
                  <span>•</span>
                  <span>Đang hoạt động</span>
                </div>

                <Button
                  className={`w-full ${
                    device.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700' :
                    device.color === 'green' ? 'bg-green-600 hover:bg-green-700' :
                    device.color === 'orange' ? 'bg-orange-600 hover:bg-orange-700' :
                    'bg-purple-600 hover:bg-purple-700'
                  } text-white group-hover:opacity-90`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeviceSelect(device.id);
                  }}
                >
                  <Monitor className="h-4 w-4 mr-2" />
                  Giám sát thiết bị
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>

              {/* Gradient overlay */}
              <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl ${
                device.color === 'blue' ? 'from-blue-400/20' :
                device.color === 'green' ? 'from-green-400/20' :
                device.color === 'orange' ? 'from-orange-400/20' :
                'from-purple-400/20'
              } to-transparent rounded-bl-full`}></div>
            </Card>
          ))}
        </div>

        {/* System Info */}
        <div className="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200 p-6">
          <div className="flex items-start gap-4">
            <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-blue-100">
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Thông tin hệ thống
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Tổng số thiết bị:</span>
                  <span className="ml-2 font-semibold text-gray-900">4 ổ cắm</span>
                </div>
                <div>
                  <span className="text-gray-600">Trạng thái hệ thống:</span>
                  <span className="ml-2 font-semibold text-green-600">Hoạt động bình thường</span>
                </div>
                <div>
                  <span className="text-gray-600">Cập nhật cuối:</span>
                  <span className="ml-2 font-semibold text-gray-900">Vừa xong</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceSelectionPage;
